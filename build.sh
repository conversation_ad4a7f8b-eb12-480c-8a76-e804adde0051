#!/bin/bash

# build.sh should not be sourced
[[ "${BASH_SOURCE[0]}" != "${0}" ]] && {
		echo "${BASH_SOURCE[0]} can only be executed and should not be sourced ";
		return 0;
		}

# nullglob is needed in case one or more of the search meta layer
# search paths does not contain any meta layer. If nullglob is not set,
# we would append the string "${CURDIR}/*/*/meta-*" to $LAYERS. This
# causes the WR setup script to fail.
shopt -s nullglob

cd "$(dirname "${BASH_SOURCE[0]}")" || { echo "cd $(dirname "${BASH_SOURCE[0]}") failed."; exit 1; }

# Check for PREMIRROR incompatible file system path
repo_top_url="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
premirror_replacements=("TYPE" "HOST" "PATH" "BASENAME" "MIRRORNAME")
for keyword in "${premirror_replacements[@]}"; do
	if [[ $repo_top_url =~ $keyword ]]; then
		echo "Path to build repo: $repo_top_url should not contain any of: ${premirror_replacements[*]}"
		exit 1
	fi
done

# Source common build functions
# shellcheck source=common.sh
source "tools/build-tools/common.sh"

# Source windriver specific, product global, variables
# shellcheck disable=SC1090
if [[ -f "${CONFIG_DIR}"/linux_distribution.sh ]]; then
	source "${CONFIG_DIR}"/linux_distribution.sh
fi

# Source product specific variables
# shellcheck disable=SC1090
source "${CONFIG_DIR}"/product.sh
if [ -f "${CONFIG_DIR}"/common-configuration/common-product.sh ]; then
	# shellcheck disable=SC1090
	source "${CONFIG_DIR}"/common-configuration/common-product.sh
	# Combine the common path with the the specific path
        SSTATE_MIRROR_DIR="${NFS_MOUNT}${SSTATE_MIRROR_DIR}"
        SSTATE_MIRRORS="file://${SSTATE_MIRROR_DIR}"

fi

MANPAGE="tools/build-tools/$(basename "${BASH_SOURCE[0]}").1"

WITH_LAYERS=""
BB_VARS=""
BB_VARS_FV=""

# Enable the PR service for recipes? <yes|local|<hostname:port>|no>
# "local" or "yes" means use the bitbake local PR service.  If you have
# a shared PR server, you can also specify a hostname/IP address and a port
# where the service is listening.
PRSERVER="local"

BB_NUMBER_THREADS=$(nproc)
PARALLEL_MAKE=$(nproc)

LOCAL_PROJ_CONF_FILE="/conf/local-project.conf"

mostlyclean() {
	cmd "rm -rf ${BB_BUILD_DIR}/tmp*/work*" --verbose
}

clean() {
	cmd "rm -rf ${WORKSPACE_DIR}" --verbose
}

bgclean() {
	local tmp_dir
	tmp_dir="${BUILD_DIR}$(mktemp --dry-run --tmpdir="/")"
	cmd "mv ${WORKSPACE_DIR} ${tmp_dir}" --verbose
	cmd "rm -rf ${tmp_dir}&" --verbose
}

distclean() {
	cmd "rm -rf ${BUILD_DIR}" --verbose
}

bbs() {
	cmd "${BB_CMD_PREFIX} /bin/bash"
}

bb() {
	bitbake_cmd "$BITBAKE_ARGS"
}

add_layers() {
	# Update bblayers conf file with our layers.
	local layers
	for layer in ${WITH_LAYERS//,/" "} ; do
		layer="$(readlink -f "${layer}")"
		layers="${layers} ${layer}"
	done
	cmd "${BB_SOURCE_ENV}"
	cmd "bitbake-layers add-layer ${layers}"  --verbose
}

######################################################################
# WR configure (create project build directory
setup_wrl8(){
	if [[ "${WITH_LAYERS}" == "" ]]; then
		for meta in "${CURDIR}"/*/meta-* "${CURDIR}"/*/*/meta-* ; do
			LAYERS+=${meta},
		done
	else
		for layer in ${WITH_LAYERS//,/" "} ; do
			LAYERS+=$(readlink -f "${layer}"),
		done
	fi

	if [ "$DOMAIN_SSTATE_MIRROR" ]; then
		local mirror="file://${DOMAIN_SSTATE_MIRROR}/${BUILD}/${BSP}"
		SSTATE_MIRRORS="${SSTATE_MIRRORS},${mirror}"
	fi

	# Temp hack. This should go in to linux_distribution.sh
	LAYERS+="wr-kernel/kernel-dev"

	WR_CONFIG_OPTS+=("--enable-buildtools=no")
	WR_CONFIG_OPTS+=("--with-rcpl-version=${WRL_RCPL_VER}")
	WR_CONFIG_OPTS+=("--with-layer=${LAYERS}")
	WR_CONFIG_OPTS+=("--enable-rootfs=${ROOTFS}")
	WR_CONFIG_OPTS+=("--enable-kernel=${KERNEL}")
	WR_CONFIG_OPTS+=("--with-board=${BSP}")
	WR_CONFIG_OPTS+=("--with-init=${INIT}")
	WR_CONFIG_OPTS+=("--with-sstate-dir=${SSTATE_DIR}")
	if [ ! "$DISABLE_SSTATE_MIRROR" ]; then
		WR_CONFIG_OPTS+=("--with-ro-sstate-mirror=${SSTATE_MIRRORS}")
	fi
	WR_CONFIG_OPTS+=("--enable-reconfig")
	WR_CONFIG_OPTS+=("--enable-parallel-pkgbuilds=${BB_NUMBER_THREADS}")
	WR_CONFIG_OPTS+=("--enable-jobs=${PARALLEL_MAKE}")
	WR_CONFIG_OPTS+=("--enable-prserver=${PRSERVER}")
	WR_CONFIG_OPTS+=("--enable-buildstats=${BUILDSTATS}")
	if [ ! "$DISABLE_BUILD_HISTORY" ]; then
		local opt="--enable-buildhist=yes --with-buildhist-dir=${BUILDHISTDIR}"
		WR_CONFIG_OPTS+=("${opt}")
	fi


	WR_CONFIG_OPTS+=("--with-dl-dir=$DL_DIR")
	dl_dir_permissions="700"
	if [[ -e "${DL_DIR}" ]]; then
		cmd "chmod ${dl_dir_permissions} ${DL_DIR}"
	else
		cmd "install --directory ${DL_DIR} --mode=${dl_dir_permissions}"
	fi

	local configure="${WRL_HOME}/wrlinux-${WRL_MAJOR_VER}/wrlinux/configure"
	local cmd="cd ${WORKSPACE_DIR} && $configure ${WR_CONFIG_OPTS[*]}"
	cmd "${cmd}" --verbose
}

setup_wrl_lts(){
	local layers=""
	if [[ "${WRL_RCPL_VER}" == "WRLINUX_10_18"* ]] ||  [[ "${WRL_RCPL_VER}" == "WRLINUX_10_21"* ]]; then
		local kdev_layer="${WORKSPACE_DIR}/layers/wrlinux/wrlinux-kernel-dev"
	else
		local kdev_layer=""
	fi

	if [[ "${WITH_LAYERS}" == "" ]]; then
		for meta in "${CURDIR}"/*/meta-* "${CURDIR}"/*/*/meta-* ; do
			if [[ "$meta" == *"${CURDIR}/wrl-rcs/layers/meta-wr-vrcs-"* ]]; then
				continue
			fi
			layers="${layers} ${meta}"
		done
	else
		for layer in ${WITH_LAYERS//,/" "} ; do
			layer="$(readlink -f "${layer}")"
			layers="${layers} ${layer}"
		done
	fi

	WRL_REPO="wrlinux-${WRL_MAJOR_VER}"
	WRL_URL="${WRL_MIRROR}/${WRL_REPO}"

        export REPO_MIRROR_LOCATION="${WRL_MIRROR}"
	if [ ! -d "${WORKSPACE_DIR}"/"${WRL_REPO}" ] ; then
		cmd="cd ${WORKSPACE_DIR} && "
		cmd+="git clone -q --branch ${WRL_RCPL_VER} ${WRL_URL}"
	else
		cmd="cd ${WORKSPACE_DIR}/${WRL_REPO} && "
		cmd+="git checkout ${WRL_RCPL_VER} && "
		cmd+="git pull"
	fi
	cmd "${cmd}" --verbose

	cmd="cd ${WORKSPACE_DIR} && "
	cmd+="${WRL_REPO}/setup.sh "
	cmd+="--accept-eula yes "
	cmd+="--dl-layers "
	# setup.sh does not recognize any of our BSPs, use qemuarm for now.
	#cmd+="--machine ${BSP} "
	if [ x"${SETUP_BSP}" = x"" ]; then
		SETUP_BSP=qemuarm
	fi
	cmd+="--machine ${SETUP_BSP} "
	cmd "${cmd}" --verbose

	# Create build directory
	cmd "${BB_SOURCE_ENV}"

	# Update bblayers conf file with our layers.
	cmd "bitbake-layers add-layer ${layers} ${kdev_layer}"  --verbose
	# In order to enable the layer that contains linux-yocto custom,
	# the below setting is required.
	append_file "ENABLE_KERNEL_DEV=\"1\"" "${BB_BUILD_DIR}/conf/bblayers.conf"

	# Temporary bump the bitbake version in OE core
	if [ "${WRL_RCPL_VER}" == "WRLINUX_10_18_LTS_RCPL0005" ]; then
		cmd "cd ${WORKSPACE_DIR}/bitbake && git checkout 6c6540d"
	fi
}

setup_wrl_lts_pre_build() {
	WRL_REPO="wrlinux-${WRL_MAJOR_VER}"
	WRL_URL="${WRL_MIRROR}/${WRL_REPO}"

	export REPO_MIRROR_LOCATION="${WRL_MIRROR}"
	if [ ! -d "${WORKSPACE_DIR}"/"${WRL_REPO}" ] ; then
		cmd="cd ${WORKSPACE_DIR} && "
		cmd+="git clone -q --branch ${WRL_RCPL_VER} ${WRL_URL}"
	else
		cmd="cd ${WORKSPACE_DIR}/${WRL_REPO} && "
		cmd+="git checkout ${WRL_RCPL_VER} && "
		cmd+="git pull"
	fi
	cmd "${cmd}" --verbose
}

run_wrl_lts_setup_script() {
	WRL_REPO="wrlinux-${WRL_MAJOR_VER}"
	cmd="cd ${WORKSPACE_DIR} && "
	cmd+="${WRL_REPO}/setup.sh "
	cmd+="--accept-eula yes "
	cmd+="--dl-layers "
	# setup.sh does not recognize any of our BSPs, use qemuarm for now.
	#cmd+="--machine ${BSP} "
	if [ x"${SETUP_BSP}" = x"" ]; then
		SETUP_BSP=qemuarm
	fi
	cmd+="--machine ${SETUP_BSP} "
	cmd "${cmd}" --verbose
}

setup_wrl_lts_on_build() {
	local layers=""
	local kdev_layer="${WORKSPACE_DIR}/layers/wrlinux/wrlinux-kernel-dev"
	if [[ "${WITH_LAYERS}" == "" ]]; then
		for meta in "${CURDIR}"/*/meta-* "${CURDIR}"/*/*/meta-* ; do
			layers="${layers} ${meta}"
		done
	else
		for layer in ${WITH_LAYERS//,/" "} ; do
			layer="$(readlink -f "${layer}")"
			layers="${layers} ${layer}"
		done
	fi

	# Create build directory
	cmd "${BB_SOURCE_ENV}"

	# Update bblayers conf file with our layers.
	cmd "bitbake-layers add-layer ${layers} ${kdev_layer}"  --verbose

	# Temporary bump the bitbake version in OE core
	if [ "${WRL_RCPL_VER}" == "WRLINUX_10_18_LTS_RCPL0005" ]; then
		cmd "cd ${WORKSPACE_DIR}/bitbake && git checkout 6c6540d"
	fi
}
setup_distro(){
	local layers=""
	if [[ "${WITH_LAYERS}" == "" ]]; then
		for meta in "${CURDIR}"/*/meta-* "${CURDIR}"/*/*/meta-* "${CURDIR}"/*/*/layers/meta-*; do
			# Dont look for metalayers in any of these paths
			if [[ "${meta}" =~ "${CURDIR}/build/".* ||
				"${meta}" =~ "${CURDIR}/deploy/".* ||
				"${meta}" =~ "${CURDIR}/elin/".* ||
				"${meta}" == "${CURDIR}/intel/meta-intel-axxia" ||
				"${meta}" == "${CURDIR}/intel/meta-intel-axxia/meta-intel-distro" ]]; then
				continue
			fi
			# ras builds use recepies from these metalayers
			if [[ "${meta}" =~ "${CURDIR}/ras/meta-ras" ]]; then
                                layers="${layers} ${CURDIR}/elin/meta-openembedded/meta-webserver"
				layers="${layers} ${CURDIR}/elin/openembedded-core/meta"
                        fi
			layers="${layers} ${meta}"
		done
	else
		for layer in ${WITH_LAYERS//,/" "} ; do
			layer="$(readlink -f "${layer}")"
			layers="${layers} ${layer}"
		done
	fi

	if [ "${DISTRO_AS_SUBMODULE}" == "false" ] ; then
		local distro_url="${DISTRO_MIRROR}/${DISTRO_REPO}"
		if [ ! -d "${WORKSPACE_DIR}"/"${DISTRO_REPO}" ] ; then
			cmd="cd ${WORKSPACE_DIR} &&"
			cmd+="git clone --quiet --depth 1 --branch ${DISTRO_VER} ${distro_url}"
		else
			cmd="cd ${WORKSPACE_DIR}/${DISTRO_REPO} &&"
			cmd+="git fetch &&"
			cmd+="git checkout ${DISTRO_VER}"
		fi
		cmd "${cmd}" --verbose
	fi

	if [ "${EST_BUILD}" == "true" ]; then
		local setup_opts="-e"
	else
		local setup_opts=""
	fi
	cmd="cd ${ELIN_DIR}/${DISTRO_REPO} && "
	cmd+="./setup ${setup_opts} -d ${WORKSPACE_DIR}"
	cmd "${cmd}" --verbose

	# Create build directory
	cmd="cd ${WORKSPACE_DIR} && "
	cmd+="${BB_SOURCE_ENV}"
	cmd "${cmd}"

	# Update bblayers conf file with our layers.
	if [ "" != "${layers}" ]; then
		cmd "bitbake-layers add-layer ${layers}"  --verbose
	fi
}

setup_wr_project() {
	cmd "mkdir -p ${SSTATE_DIR}"
	cmd "mkdir -p ${WORKSPACE_DIR}"

	if [[ -n "${DISTRO_VER}" || "${DISTRO_AS_SUBMODULE}" == "true" ]]; then
		setup_distro
	elif [ "x${WRL_VER}" == "xwrl-lts" ]; then
		if [ "$SETUP_PRE_BUILD" ]; then
			setup_wrl_lts_pre_build
			run_wrl_lts_setup_script
			echo ""
			echo "BUILD REPO PRE-SETUP FINISHED."
			echo "CONTINUE SETUP AT BUILD TIME WITH:"
			echo "--setup-build"
			echo ""
			exit 0
		fi
		if [ "$SETUP_ON_BUILD" ]; then
			run_wrl_lts_setup_script
			setup_wrl_lts_on_build
		fi
		if [ -z "$SETUP_PRE_BUILD" ] && [ -z "$SETUP_ON_BUILD" ]; then
			setup_wrl_lts
		fi

		# Remove --mirror from git clone
		cmd "sed -i 's/--bare --mirror/--bare/g' ${WORKSPACE_DIR}/bitbake/lib/bb/fetch2/git.py"

		local oe_core_bb_path="${WORKSPACE_DIR}/layers/oe-core/bitbake"
		local oe_core_path="${WORKSPACE_DIR}/layers/oe-core"
		if [ "$ENABLE_BITBAKE_DEBUG" ] && [[ "${WRL_RCPL_VER}" == "WRLINUX_10_18_LTS_RCPL00"* ]]; then
			apply_wrl_bb_patch "bb-debug.patch" "$oe_core_bb_path"
		elif [ "$ENABLE_BITBAKE_DEBUG" ]; then
			echo "--enable-bitbake-debug is not supported for Linux distro $WRL_VER RCPL $WRL_RCPL_VER"
		fi

	        if [[ "${WRL_RCPL_VER}" == "WRLINUX_10_21_LTS_RCPL00"* ]]; then
	                local wrl_rcpl_ver="${WRL_RCPL_VER#WRLINUX_10_21_LTS_RCPL}"
			# Remove leading 0 so that -ge and -le works
			wrl_rcpl_ver=$((10#$wrl_rcpl_ver))

			if [ "${wrl_rcpl_ver}" -le 4 ]; then
				apply_wrl_bb_patch "wrl21-data_smart-Allow-colon-in-variable-expansion-regex.patch" "$oe_core_bb_path"
			fi
	                if [[ "${wrl_rcpl_ver}" -ge 5 && "${wrl_rcpl_ver}" -le 10 ]]; then
				apply_wrl_bb_patch "sdk_symlink_fix.patch" "$oe_core_path"
	                fi
		elif [[ "${WRL_RCPL_VER}" == "WRLINUX_10_18_LTS_RCPL00"* ]]; then
			apply_wrl_bb_patch "wrl18-data_smart-Allow-colon-in-variable-expansion-regex.patch" "$oe_core_bb_path"
		fi
	else
		apply_wrl_bb_patch "wrl8-data_smart-Allow-colon-in-variable-expansion-regex.patch" "${WORKSPACE_DIR}/bitbake"
		setup_wrl8
	fi
	local_conf
	local_project_conf
	rstate_conf
	bb_vars_conf
	meta_layers_conf
	apply_patches

	# Create link to deploy for convenience
	#
	# POKY_DEPLOY_DIR can change and also "deploy" might be a file or
	# directory that was intentionally called deploy and should
	# therefore not just be deleted without a check.

	# First; If soft link then assume it's a link that were created in a
	# previous setup and should be remove since it's potentially stale.
	if [ -L "${CURDIR}"/deploy ] ; then
		cmd="rm -f ${CURDIR}/deploy"
		cmd "${cmd}"
	fi
	# If "deploy" happened to not be a link, that would have been deleted
	# above, then keep whatever it is. Else create (re-)create the soft link.
	if [ ! -e "${CURDIR}"/deploy ] ; then
		cmd="ln -s ${POKY_DEPLOY_DIR} ${CURDIR}/deploy"
		cmd "${cmd}"
	fi

	# Be backward compatible and create a symlink that exists in WRL8.
	if [[ "wrlinux8" != "${WRL_VER}" ]]; then
		local export_dir="${WORKSPACE_DIR}/export"
		if [ ! -e "${export_dir}" ] ; then
			cmd "mkdir ${export_dir}"
			cmd="ln -s ${POKY_DEPLOY_DIR}/images/${BSP} ${export_dir}/images"
			cmd "${cmd}"
		fi
	fi
}

apply_wrl_bb_patch() {
	local patch=$1
	local bb_path=$2
	if [ "x${WRL_VER}" == "xwrlinux8" ]; then
		if ! [ -d "$bb_path" ]; then
			# We have to clone git/bitbake since we want to apply the patch before the parsing take place
			git clone --branch LB13_8.0_RCPL"${WRL_RCPL_VER}" "${WRL_HOME}/wrlinux-${WRL_MAJOR_VER}/git/bitbake" "$bb_path"
		fi
	fi

	if ! [ -f "$CURDIR/build/applied-patches/$patch".applied ]; then
		echo "Patching build with $patch"
		pushd "${bb_path}" || { echo "pushd ${bb_path} failed."; exit 1; }
		local patchfile=$CURDIR/tools/build-tools/$patch
		patch -p1 < "$patchfile"
		popd || { echo "popd from  ${bb_path} failed."; exit 1; }
		mkdir -p "$CURDIR"/build/applied-patches
		touch "$CURDIR/build/applied-patches/$patch".applied
		echo "Finished patching build with $patch"
	else
		echo "The build is patched with $patch"
	fi
}

apply_patches() {
	if [ -f "$CURDIR"/patches/patch.list ]; then
		mkdir -p "$CURDIR"/build/applied-patches
		while read -r line; do
			IFS=" " read -r -a patch_array <<< "$line"
			local relpath=${patch_array[0]}
			local patch=${patch_array[1]}
			if ! [ -f "$CURDIR/build/applied-patches/$patch".applied ]; then
				pushd "${WORKSPACE_DIR}/$relpath" > /dev/null  || { echo "pushd ${WORKSPACE_DIR}/$relpath failed."; exit 1; }
				local cmd="patch -p1 < $CURDIR/patches/$patch"
				cmd "${cmd}" --verbose
				popd > /dev/null || { echo "popd from  ${WORKSPACE_DIR}/$relpath failed."; exit 1; }
				touch "$CURDIR/build/applied-patches/$patch".applied
			else
				echo "Skipping $patch already applied."
			fi
		done < "$CURDIR/patches/patch.list"
	fi
}

git_status() {
	if [ "$1" == "" ]; then
		echo "clean"
	else
		echo "dirty"
	fi
}

is_meta_layer_selected() {
	local select=false
	for selected in ${WITH_LAYERS//,/" "}; do
		if [ "$1" == "$(basename "$selected")" ]; then
			select=true
		fi
	done
	echo $select
}

submodule_urls() {
	local file path path_expr url_expr
	pushd "$CURDIR" > /dev/null || { echo "pushd $CURDIR failed."; exit 1; }
	META_LAYER_URLS=()
	META_LAYER_STATUSES=()
	file=$(mktemp)
	git submodule > "$file"
	while read -r line; do
		path=$(echo "$line" | cut -d' ' -f2);
		if [[ "${WITH_LAYERS}" == "" ]]; then
			path_expr="${path////\\/}"
			url_expr=$(sed -n "/path = $path_expr$/{n;p}" "${CURDIR}"/.gitmodules)
			mapfile -t meta_layer_urls_array < <(echo "$url_expr" | cut -d' ' -f3)
			META_LAYER_URLS+=("${meta_layer_urls_array[@]}")
			pushd "$path" > /dev/null || { echo "pushd $path failed."; exit 1; }
			mapfile -t meta_layer_statuses_array < <(git_status "$(git status --porcelain)")
			META_LAYER_STATUSES+=("${meta_layer_statuses_array[@]}")
			popd > /dev/null || { echo "popd from  $path failed."; exit 1; }
		else
			local select
			select=$(is_meta_layer_selected "$(basename "$path")")
			if [ "$select" == "true" ]; then
				path_expr="${path////\\/}"
				url_expr=$(sed -n "/path = $path_expr$/{n;p}" "${CURDIR}"/.gitmodules)
				mapfile -t meta_layer_urls_array < <(echo "$url_expr" | cut -d' ' -f3)
				META_LAYER_URLS+=("${meta_layer_urls_array[@]}")
				pushd "$path" > /dev/null || { echo "pushd $path failed."; exit 1; }
				mapfile -t meta_layer_statuses_array < <(git_status "$(git status --porcelain)")
				META_LAYER_STATUSES+=("${meta_layer_statuses_array[@]}")
				popd > /dev/null || { echo "popd from  $path failed."; exit 1; }
			fi
		fi
	done < "$file"
	rm "$file"
	popd > /dev/null || { echo "popd from  $CURDIR failed."; exit 1; }
}

submodule_vsns() {
	local file path vsn
	pushd "$CURDIR" > /dev/null || { echo "pushd $CURDIR failed."; exit 1; }
	META_LAYER_VSNS=()
	file=$(mktemp)
	git submodule > "$file"
	while read -r line; do
		path=$(echo "$line" | cut -d' ' -f2);
		if [[ "${WITH_LAYERS}" == "" ]]; then
			vsn=$(echo "$line" | cut -d' ' -f1);
			META_LAYER_VSNS+=("$vsn")
		else
			local select
			select=$(is_meta_layer_selected "$(basename "$path")")
			if [ "$select" == "true" ]; then
				vsn=$(echo "$line" | cut -d' ' -f1);
				META_LAYER_VSNS+=("$vsn")
			fi
		fi

	done < "$file"
	rm "$file"
	popd > /dev/null || { echo "popd from  $CURDIR failed."; exit 1; }
}

copy_mdcs() {
	local mdc_dir="${POKY_DEPLOY_DIR}/manifest/mdc"
	if [ -d "$mdc_dir" ]; then
		cmd "rm -rf $mdc_dir/*"
	else
		cmd "mkdir -p $mdc_dir"
	fi

	if [[ "${WITH_DOMAINS}" == "" ]]; then
		while IFS= read -r -d '' mdc ; do
			mdcname=$(basename "$(dirname "$mdc")")
			cmd "mkdir -p $mdc_dir/$mdcname"
			cmd "cp $mdc/* $mdc_dir/$mdcname"
		done < <(find "${CURDIR}" -maxdepth 2 -name mdc -type d -print0)
	else
		for domain in $WITH_DOMAINS; do
			cmd "mkdir -p $mdc_dir/$domain"
			cmd "cp ${CURDIR}/$domain/mdc/* $mdc_dir/$domain"
		done
	fi
}

bitbake_cmd() {
	local cmd="${BB_CMD_PREFIX} bitbake $*"
	cmd "${cmd}" --verbose
}

bb_vars_conf() {
	local file_content var_separator var_syms
	shopt -s extglob

	var_separator=";"
	# Symbols that can be contained in variable names and values
	var_syms="([a-z A-Z 0-9 _\[\] \":!%\-,() ])"
	local IFS="$var_separator"

	# Trim all whitespaces
	BB_VARS_FV="${BB_VARS_FV// /}"; BB_VARS="${BB_VARS// /}"

	# Escape all square brackets and question marks. This is needed for iterating over the lists.
	BB_VARS_FV="${BB_VARS_FV//\[/\\[}"; BB_VARS_FV="${BB_VARS_FV//\]/\\\]}"
	BB_VARS="${BB_VARS//\[/\\[}"; BB_VARS="${BB_VARS//\]/\\\]}"
	BB_VARS_FV="${BB_VARS_FV//\?/\\?}"; BB_VARS="${BB_VARS//\?/\\?}"

	for v in $BB_VARS_FV; do
		if [[ -n "$v" ]]; then
			# Remove escaping backslashes
			v="${v//\\/}";
			# Remove all variables symbols to get operator
			operator="${v//+$var_syms/}"
			# '_forcevariable' needs to be added in different positions depending on square brackets
			if [[ ! "${v//\[/}" == "${v}" ]]; then
				v="${v//[/_forcevariable[}"
				v="${v//${operator}/ $operator }"
			else
				v="${v//${operator}/_forcevariable ${operator} }"
			fi
			file_content+="${v}\n"
		fi
	done

	for v in $BB_VARS; do
		if [[ -n "${v}" ]]; then
			# Remove escaping backslashes
			v="${v//\\/}";
			# Remove all variables symbols to get operator
			operator="${v//+$var_syms/}"
			v="${v//${operator}/ ${operator} }"
			file_content+="${v}\n"
	fi
	done
	if [[ -n "${file_content}" ]]; then
		append_file "${file_content}" "${BB_BUILD_DIR}${LOCAL_PROJ_CONF_FILE}"
	fi
	shopt -u extglob
}

meta_layers_conf() {
	local manifest_conf
	submodule_vsns
	submodule_urls
	copy_mdcs
	BUILD_REPO_URL=$(git remote -v | head -1 | cut -f2 | cut -d' ' -f1)
	BUILD_REPO_VSN=$(git log -1 --format='%H')
	BUILD_REPO_STATUS=$(git_status "$(git status --porcelain)")

	# The escaping '\\\t' ensures that '\\t' is inserted between url and vsn
	# This then allows for bitbake shell for loop on ${META_LAYER_INFO}
	# without '"' using 'echo -e' to write to the manifest file on rootfs.
	manifest_conf="${BB_BUILD_DIR}""/conf/build_manifest.conf"

	if [[ -f "${manifest_conf}" ]]; then
		cmd "rm ${manifest_conf}"
	fi

	if [[ ${DRY_RUN} == "true" ]]; then
		append_file "BUILD_REPO_INFO += \"${BUILD_REPO_VSN}\\\t${BUILD_REPO_STATUS}\\\t${BUILD_REPO_URL}\""  \
			"${manifest_conf}"
		append_file "ERI_TOP_REPO_ABSPATH = \"${repo_top_url}\""  \
			"${manifest_conf}"
	else
		echo "BUILD_REPO_INFO += \"${BUILD_REPO_VSN}\\\t${BUILD_REPO_STATUS}\\\t${BUILD_REPO_URL}\"" > \
			"${BB_BUILD_DIR}"/conf/build_manifest.conf
		echo "ERI_TOP_REPO_ABSPATH = \"${repo_top_url}\"" >> \
			"${BB_BUILD_DIR}"/conf/build_manifest.conf
	fi

	for (( i=0; i<${#META_LAYER_URLS[@]}; i++ )); do
		if [[ ${DRY_RUN} == "true" ]]; then
			append_file "META_LAYER_INFO += \"${META_LAYER_VSNS[$i]}\\\t${META_LAYER_STATUSES[$i]}\\\t${META_LAYER_URLS[$i]}\""  \
				"${manifest_conf}"
		else
			echo "META_LAYER_INFO += \"${META_LAYER_VSNS[$i]}\\\t${META_LAYER_STATUSES[$i]}\\\t${META_LAYER_URLS[$i]}\"" >> \
				"${BB_BUILD_DIR}"/conf/build_manifest.conf
		fi
	done
}

local_conf() {
	cmd "mv ${BB_BUILD_DIR}/conf/local.conf ${BB_BUILD_DIR}/conf/local.conf.bak"
	cmd "cp ${PROJ_LOCAL_CONF} ${BB_BUILD_DIR}/conf/local.conf"
}

rstate_conf() {
	local file_content=""

	# ${CONFIG_DIR}/rstate.sh is to prevent build.sh being tightly bound
	# to a build repository. The idea is to place all build repository
	# specific bitbake variable names that must be set to RSTATE here.
	if [ -f "${CONFIG_DIR}"/rstate.sh ]; then
		while read -r variable; do
			file_content+=$variable"_forcevariable = \"${RSTATE}\""'\n'
		done < "${CONFIG_DIR}"/rstate.sh
	fi

	append_file "${file_content}" "${BB_BUILD_DIR}"/conf/rstate.conf
}

local_project_conf() {
	cmd "cp ${PROJ_BB_CONF} ${BB_BUILD_DIR}${LOCAL_PROJ_CONF_FILE}"

	local file_content="\n"

	file_content+="PREMIRRORS:prepend = ${EXTRA_PREMIRRORS}"'\n'

	file_content+="SSTATE_DIR = \"${SSTATE_DIR}\""$'\n'

	file_content+="FETCHCMD_git = \"${CURDIR}/tools/build-tools/rgit\""$'\n'
	if [ "${EST_BUILD}" == "false" ]; then
		file_content+="GIT_MIRROR = \"${GIT_MIRROR}\""$'\n'
		file_content+="GIT_PROTOCOL = \"${GIT_PROTOCOL}\""$'\n'
		file_content+="FETCHCMD_wget = \"${CURDIR}/tools/build-tools/rwget\""$'\n'
		file_content+="BB_NO_NETWORK = \"0\""$'\n'
		file_content+="BB_ALLOWED_NETWORKS = \"*.ericsson.se *.ericsson.se:8443 *.ericsson.se:29418 *.ericsson.com gerritmirror\""$'\n'
		file_content+="CONNECTIVITY_CHECK_URIS = \"https://gerrit.ericsson.se\""$'\n'
	else
		file_content+="EST_BUILD = \"True\""$'\n'
	fi

	file_content+="DL_DIR = \"${DL_DIR}\""$'\n'

	if [ ! "$DISABLE_SSTATE_MIRROR" ]; then
		file_content+="SSTATE_MIRRORS =+ \"file://.* ${SSTATE_MIRRORS}/PATH;downloadfilename=PATH \\\n\""$'\n'
	fi

	if [ "$DISABLE_STRICT_CHECKSUMS" ]; then
		file_content+="BB_STRICT_CHECKSUM = \"0\""$'\n'
	fi

	file_content+="BB_NUMBER_THREADS = \"$BB_NUMBER_THREADS\""$'\n'
	file_content+="PARALLEL_MAKE = \"-j $PARALLEL_MAKE\""$'\n'

	file_content+="HOSTTOOLS_NONFATAL += \" whoami\""$'\n'

	if [ "$ENABLE_ARCHIVER" == "true" ]; then
		file_content+="INHERIT += \"archiver\""$'\n'
		file_content+="COPYLEFT_LICENSE_EXCLUDE += \"CLOSED Proprietary ERICSSON\""$'\n'
		file_content+="ARCHIVER_MODE[src] = \"patched\""$'\n'
	fi

	if [ "${WRL_VER}" == "wrl-lts" ]; then
		file_content+="ENABLE_KERNEL_DEV = \"1\""$'\n'
	fi

	if [ "$CREATE_SRC_MANIFEST" == "true" ]; then
		file_content+="INHERIT += \"src-manifest\""$'\n'
	fi

	if [ "$ENABLE_LEGACY_UCF_HANDLING" ]; then
		file_content+="OVERRIDES:prepend = \"legacy-ucf-handling:\""$'\n'
	fi

	append_file "${file_content}" "${BB_BUILD_DIR}${LOCAL_PROJ_CONF_FILE}"
}

check_deprecated_options() {
    for word in "$@"
    do
	if [[ "${word}" == "--omit-meta-layer" || "${word}" == "--omit-domain" ]]; then
		echo -e "\n*************************************************************"
		echo "***** WARNING: ${word} is deprecated          *****"
		echo "***** Use --with-meta-layers or --with-domains instead. *****"
		echo -e "*************************************************************\n"
	fi
    done
}

set_distro_deploy_dir() {
        # Master branch reverted changes to tmp dir location
        build_repo_branch=$(git branch --show-current)
	if [[ "wrlinux8" == "${WRL_VER}" || "master" == "${build_repo_branch}" ]]; then
		POKY_DEPLOY_DIR="${BB_BUILD_DIR}/tmp/deploy"
	else
		POKY_DEPLOY_DIR="${BB_BUILD_DIR}/tmp-glibc/deploy"
	fi
}

check_argv "$1"
check_deprecated_options "$@"

case "$1" in
	bbs | bbshell) command="bbs" ;;
	bb)            command="bb"  ;;
	setup)         command="setup_wr_project" ;;
	mostlyclean)   command="mostlyclean" ;;
	clean)         command="clean" ;;
	bgclean)       command="bgclean" ;;
	distclean)     command="distclean" ;;
	addlayers)     command="add_layers" ;;
	-h | --help)
		man "$MANPAGE"
		exit 0
		;;
	*)
		echo "ERROR: Unknown command -> '$1'" >&2
		usage
		exit 0
		;;
esac
shift 1

while :
do
	case "$1" in
		-a | --enable-archiver)
			ENABLE_ARCHIVER=true
			shift 1
			;;
		--bb-vars)
			check_for_arg "$1" $#
			BB_VARS="$2"
			shift 2
			;;
		--bb-vars-fv)
			check_for_arg "$1" $#
			BB_VARS_FV="$2"
			shift 2
			;;
		-d | --build-dir)
			check_for_arg "$1" $#
			BUILD_DIR="$2"
			shift 2
			;;
		--dl-dir)
			check_for_arg "$1" $#
			DL_DIR="$2"
			shift 2
			;;
		--domain-sstate-mirror-dir)
			check_for_arg "$1" $#
			DOMAIN_SSTATE_MIRROR="$2"
			shift 2
			;;
		--git-file-premirror)
			check_for_arg "$1" $#
			GIT_FILE_PREMIRROR="$2"
			shift 2
			;;
		--git-mirror)
			check_for_arg "$1" $#
			GIT_MIRROR="$2"
			shift 2
			;;
		--git-protocol)
			check_for_arg "$1" $#
			GIT_PROTOCOL="$2"
			shift 2
			;;
		--create-src-manifest)
			CREATE_SRC_MANIFEST="true"
			shift 1
			;;
		-h | --help)
			man "$MANPAGE"
			exit 0
			;;
		-j | --dry-run)
			set_dry_run
			shift 1
			;;
		-m | --sstate-mirror-dir)
			check_for_arg "$1" $#
			SSTATE_MIRRORS="$2"
			shift 2
			;;
		-N | --disable-build-history)
			DISABLE_BUILD_HISTORY=true
			shift 1
			;;
		-n | --disable-sstate-mirror)
			DISABLE_SSTATE_MIRROR=true
			shift 1
			;;
		-P | --parallel-make)
			check_for_arg "$1" $#
			PARALLEL_MAKE="$2"
			shift 2
			;;
		-r | --rstate)
			check_for_arg "$1" $#
			RSTATE="$2"
			shift 2
			;;
		-s | --sstate-dir)
			check_for_arg "$1" $#
			SSTATE_DIR="$2"
			shift 2
			;;
		--setup-pre-build)
			SETUP_PRE_BUILD=true
			shift 1
			;;
		--setup-build)
			SETUP_ON_BUILD=true
			shift 1
			;;
		-t | --bb-number-threads)
			check_for_arg "$1" $#
			BB_NUMBER_THREADS="$2"
			shift 2
			;;
		-W | --with-meta-layers)
			check_for_arg "$1" $#
			WITH_LAYERS=$2,$WITH_LAYERS
			for meta_layer in ${2//,/" "} ; do
				WITH_DOMAINS+=" "$(dirname "$meta_layer")
			done
			WITH_DOMAINS=$(echo "$WITH_DOMAINS" | tr ' ' '\n' | sort -u | tr '\n' ' ')
			shift 2
			;;
		-w | --wrl-home)
			check_for_arg "$1" $#
			WRL_HOME="$2"
			shift 2
			;;
		-x | --with-domains)
			check_for_arg "$1" $#
			DOMAINS=${2//,/" "}
			for domain in $DOMAINS ; do
				for meta in "${CURDIR}"/"$domain"/meta-* "${CURDIR}"/"$domain"/*/meta-* ; do
					WITH_LAYERS=$meta,$WITH_LAYERS
				done
			done
			WITH_DOMAINS+="${DOMAINS}"
			WITH_DOMAINS=$(echo "$WITH_DOMAINS" | tr ' ' '\n' | sort -u | tr '\n' ' ')
			shift 2
			;;
		-z | --disable-strict-checksums)
			DISABLE_STRICT_CHECKSUMS=true
			shift 1
			;;
		--enable-bitbake-debug)
			ENABLE_BITBAKE_DEBUG=true
			shift 1
			;;
		--enable-legacy-ucf-handling)
			ENABLE_LEGACY_UCF_HANDLING=true
			shift 1
			;;
		--)
			shift 1
			BITBAKE_ARGS=$*
			break
			;;
		*[!\ ]*)
			echo "ERROR: Unknown option -> '$1'" >&2
			usage
			exit 1
			;;
		*)
			break
			;;
	esac
done

BUILDHISTDIR=${BUILD_DIR}/buildhistory
BUILDSTATS="yes"

FILE_PREMIRRORS="git://.*/.* git://${GIT_FILE_PREMIRROR}/PATH;protocol=file;user= \\\n \\
git://${GIT_MIRROR}/(.*) git://${GIT_FILE_PREMIRROR}/\1;protocol=file;user= \\\n \\
gitsm://.*/.* gitsm://${GIT_FILE_PREMIRROR}/PATH;protocol=file;user= \\\n \\
gitsm://${GIT_MIRROR}/(.*) git://${GIT_FILE_PREMIRROR}/\1;protocol=file;user= \\\n \\"

if [[ "$EST_BUILD" == "false" ]]; then
CS_PREMIRRORS="git://gerrit.ericsson.se:29418/.* git://$(dirname "$GIT_MIRROR"):29418/PATH \\\n \\
git://**************/DUSg2/.* git://$(dirname "$GIT_MIRROR"):29418/wrl-sync/BASENAME \\\n \\
git://**************/eprime/(.*) git://$(dirname "$GIT_MIRROR"):29418/wrl-sync/\1 \\\n \\
git://.*/.* git://${WR_DL_DIR}/BASENAME;protocol=file \\\n \\
http://.*/.* file://${WR_DL_DIR}/ \\\n \\"
else
CS_PREMIRRORS="\\"
fi

if [[ "$GIT_FILE_PREMIRROR" ]]; then
EXTRA_PREMIRRORS="\"\\
${FILE_PREMIRRORS}
${CS_PREMIRRORS}
\""
else
EXTRA_PREMIRRORS="\"\\
${CS_PREMIRRORS}
\""
fi

set_workspace_dir
if [ -d "${CURDIR}"/elin ] ; then
	ELIN_DIR="${CURDIR}/elin"
	DISTRO_AS_SUBMODULE="true"
else
	ELIN_DIR="${WORKSPACE_DIR}/elin"
	DISTRO_AS_SUBMODULE="false"
fi

if [[ "$command" == "bb" || "$command" == "bbs" ]] && [ ! -d "${WORKSPACE_DIR}" ] && [ ! "$DRY_RUN" == true ]; then
	echo "ERROR: You must setup a build directory before -> '$command'" >&2
	usage
	exit 2
fi

set_bb_build_dir
set_distro_deploy_dir

set_sstate_dir
set_bb_cmd_prefix

print_build_variables

eval $command
